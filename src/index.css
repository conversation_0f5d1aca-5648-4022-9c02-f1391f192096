@tailwind base;
@tailwind components;
@tailwind utilities;

/* Responsive button utilities */
@layer utilities {
  .btn-responsive {
    @apply text-xs px-2 py-1.5 h-auto min-h-[32px];
  }

  .btn-responsive-sm {
    @apply text-xs px-1.5 py-1 h-auto min-h-[28px];
  }

  .btn-grid-3 {
    @apply grid grid-cols-1 sm:grid-cols-3 gap-1.5;
  }

  .btn-grid-2 {
    @apply grid grid-cols-2 gap-1.5;
  }

  .btn-text-truncate {
    @apply truncate max-w-full;
  }
}

/* Custom animations for Recipe Library */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 30 10% 15%;

    --card: 0 0% 100%;
    --card-foreground: 30 10% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 30 10% 15%;

    --primary: 24 82% 58%;
    --primary-foreground: 0 0% 98%;

    --secondary: 25 95% 96%;
    --secondary-foreground: 30 10% 15%;

    --muted: 25 95% 96%;
    --muted-foreground: 30 5% 45%;

    --accent: 120 50% 92%;
    --accent-foreground: 30 10% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 25 30% 90%;
    --input: 25 30% 90%;
    --ring: 24 82% 58%;

    /* Food-specific design tokens */
    --orange-warm: 24 82% 58%;
    --orange-light: 25 95% 96%;
    --green-fresh: 120 50% 50%;
    --green-light: 120 50% 92%;
    --yellow-rating: 45 95% 60%;
    --red-favorite: 0 84% 60%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Hide scrollbar for horizontal scroll */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}