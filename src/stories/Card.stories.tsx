import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Heart, Clock, Users, Star, ChefHat, Bookmark } from 'lucide-react';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent, 
  CardFooter,
  CardImage 
} from '@/components/design-system/atoms/Card';
import { Button, IconButton } from '@/components/design-system/atoms/Button';

const meta = {
  title: 'Design System/Atoms/Card',
  component: Card,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Flexible Card component with multiple variants and compound components for building rich content layouts.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'elevated', 'outlined', 'ghost', 'gradient'],
      description: 'Visual style variant of the card',
    },
    padding: {
      control: 'select',
      options: ['none', 'sm', 'md', 'lg', 'xl'],
      description: 'Internal padding of the card',
    },
    interactive: {
      control: 'boolean',
      description: 'Whether the card is clickable/interactive',
    },
  },
  args: {
    onClick: fn(),
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Card Stories
export const Default: Story = {
  args: {
    children: (
      <div>
        <h3 className="text-lg font-semibold mb-2">Default Card</h3>
        <p className="text-gray-600">This is a basic card with default styling.</p>
      </div>
    ),
  },
};

export const Elevated: Story = {
  args: {
    variant: 'elevated',
    children: (
      <div>
        <h3 className="text-lg font-semibold mb-2">Elevated Card</h3>
        <p className="text-gray-600">This card has elevated shadow styling.</p>
      </div>
    ),
  },
};

export const Outlined: Story = {
  args: {
    variant: 'outlined',
    children: (
      <div>
        <h3 className="text-lg font-semibold mb-2">Outlined Card</h3>
        <p className="text-gray-600">This card has a prominent border.</p>
      </div>
    ),
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: (
      <div>
        <h3 className="text-lg font-semibold mb-2">Ghost Card</h3>
        <p className="text-gray-600">This card has minimal styling.</p>
      </div>
    ),
  },
};

export const Gradient: Story = {
  args: {
    variant: 'gradient',
    children: (
      <div>
        <h3 className="text-lg font-semibold mb-2">Gradient Card</h3>
        <p className="text-gray-600">This card has a gradient background.</p>
      </div>
    ),
  },
};

// Interactive Card
export const Interactive: Story = {
  args: {
    interactive: true,
    children: (
      <div>
        <h3 className="text-lg font-semibold mb-2">Interactive Card</h3>
        <p className="text-gray-600">Click me! I'm interactive with hover effects.</p>
      </div>
    ),
  },
};

// Compound Components
export const WithCompoundComponents: Story = {
  render: () => (
    <Card className="w-80">
      <CardHeader>
        <CardTitle>Recipe Card</CardTitle>
        <CardDescription>
          A delicious recipe you'll love to cook
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-600">
          This recipe combines fresh ingredients with simple techniques to create
          an amazing dish that's perfect for any occasion.
        </p>
      </CardContent>
      <CardFooter justify="between">
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Clock className="w-4 h-4" />
          <span>30 min</span>
        </div>
        <Button size="sm">View Recipe</Button>
      </CardFooter>
    </Card>
  ),
};

// Recipe Card Example
export const RecipeCard: Story = {
  render: () => (
    <Card className="w-80" interactive>
      <CardImage
        src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop"
        alt="Delicious pasta dish"
        aspectRatio="video"
      />
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle size="md">Creamy Pasta Carbonara</CardTitle>
            <CardDescription>
              Classic Italian pasta with eggs, cheese, and pancetta
            </CardDescription>
          </div>
          <IconButton
            icon={<Heart className="w-4 h-4" />}
            aria-label="Add to favorites"
            variant="ghost"
            size="sm"
          />
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            <span>25 min</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            <span>4 servings</span>
          </div>
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
            <span>4.8</span>
          </div>
        </div>
      </CardContent>
      <CardFooter justify="between">
        <div className="flex gap-2">
          <Button size="sm" variant="outline">
            <Bookmark className="w-4 h-4" />
          </Button>
          <Button size="sm">
            <ChefHat className="w-4 h-4" />
            Cook Now
          </Button>
        </div>
      </CardFooter>
    </Card>
  ),
};

// Different Padding Sizes
export const PaddingSizes: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4">
      <Card padding="sm">
        <h4 className="font-medium">Small Padding</h4>
        <p className="text-sm text-gray-600">Compact card layout</p>
      </Card>
      <Card padding="md">
        <h4 className="font-medium">Medium Padding</h4>
        <p className="text-sm text-gray-600">Default card layout</p>
      </Card>
      <Card padding="lg">
        <h4 className="font-medium">Large Padding</h4>
        <p className="text-sm text-gray-600">Spacious card layout</p>
      </Card>
      <Card padding="xl">
        <h4 className="font-medium">Extra Large Padding</h4>
        <p className="text-sm text-gray-600">Very spacious layout</p>
      </Card>
    </div>
  ),
};

// Card Grid Layout
export const CardGrid: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl">
      {[1, 2, 3, 4, 5, 6].map((i) => (
        <Card key={i} variant="default" interactive>
          <CardImage
            src={`https://images.unsplash.com/photo-${1565299624946 + i}?w=400&h=200&fit=crop`}
            alt={`Recipe ${i}`}
            aspectRatio="video"
          />
          <CardHeader>
            <CardTitle size="sm">Recipe Title {i}</CardTitle>
            <CardDescription size="sm">
              A brief description of this delicious recipe
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Clock className="w-4 h-4" />
                <span>{20 + i * 5} min</span>
              </div>
              <Button size="sm" variant="outline">
                View
              </Button>
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
};

// Card with Different Content Types
export const ContentVariations: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl">
      {/* Text Only Card */}
      <Card>
        <CardHeader>
          <CardTitle>Text Only Card</CardTitle>
          <CardDescription>
            This card contains only text content without any images
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
            Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm">Read More</Button>
        </CardFooter>
      </Card>

      {/* Image with Overlay */}
      <Card padding="none" className="relative overflow-hidden">
        <CardImage
          src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop"
          alt="Featured recipe"
          aspectRatio="video"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
        <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
          <h3 className="text-lg font-semibold mb-1">Featured Recipe</h3>
          <p className="text-sm opacity-90">Trending this week</p>
        </div>
      </Card>

      {/* Stats Card */}
      <Card variant="gradient">
        <CardHeader>
          <CardTitle>Kitchen Stats</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">24</div>
              <div className="text-sm text-gray-600">Recipes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">12</div>
              <div className="text-sm text-gray-600">Meal Plans</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Card */}
      <Card variant="outlined" interactive>
        <CardContent className="text-center">
          <ChefHat className="w-12 h-12 mx-auto mb-4 text-orange-500" />
          <CardTitle size="sm">Start Cooking</CardTitle>
          <CardDescription>
            Create your first recipe and begin your culinary journey
          </CardDescription>
        </CardContent>
        <CardFooter justify="center">
          <Button>Get Started</Button>
        </CardFooter>
      </Card>
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
};

// Dark Mode Example
export const DarkMode: Story = {
  render: () => (
    <div className="dark bg-gray-900 p-6 rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Dark Mode Card</CardTitle>
            <CardDescription>
              This card adapts to dark mode automatically
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              The card components include built-in dark mode support
              with appropriate color adjustments.
            </p>
          </CardContent>
        </Card>
        
        <Card variant="elevated">
          <CardHeader>
            <CardTitle>Elevated in Dark</CardTitle>
            <CardDescription>
              Elevated cards work great in dark mode too
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button>Action</Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  ),
};
