import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Search,
  Filter,
  RefreshCw,
  ChefHat,
  MapPin,
  Clock,
  DollarSign,
  Star,
  Users,
  Utensils,
  Coffee,
  Soup,
  Eye,
  ShoppingCart
} from 'lucide-react';
import { vietnameseFoodDataService, LoadedVietnameseFoodData } from '@/services/VietnameseFoodDataService';
import { VietnameseDish } from '@/data/vietnameseFoodCategories';
import { toast } from 'sonner';

interface VietnameseFoodExplorerProps {
  className?: string;
}

export const VietnameseFoodExplorer: React.FC<VietnameseFoodExplorerProps> = ({ className }) => {
  const [data, setData] = useState<LoadedVietnameseFoodData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [selectedMealType, setSelectedMealType] = useState('all');
  const [filteredDishes, setFilteredDishes] = useState<VietnameseDish[]>([]);

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  // Filter dishes when filters change
  useEffect(() => {
    if (data) {
      filterDishes();
    }
  }, [data, searchQuery, selectedRegion, selectedDifficulty, selectedMealType]);

  const loadData = async () => {
    try {
      setLoading(true);
      const loadedData = await vietnameseFoodDataService.loadData();
      setData(loadedData);
      toast.success('Đã load dữ liệu món ăn Việt Nam thành công!');
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Lỗi khi load dữ liệu món ăn');
    } finally {
      setLoading(false);
    }
  };

  const filterDishes = () => {
    if (!data) return;

    let filtered = data.dishes;

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(dish =>
        dish.name.toLowerCase().includes(query) ||
        dish.description.toLowerCase().includes(query) ||
        dish.ingredients.some(ingredient => ingredient.toLowerCase().includes(query))
      );
    }

    // Region filter
    if (selectedRegion !== 'all') {
      filtered = filtered.filter(dish =>
        dish.region === selectedRegion || dish.region === 'nationwide'
      );
    }

    // Difficulty filter
    if (selectedDifficulty !== 'all') {
      filtered = filtered.filter(dish => dish.difficulty === selectedDifficulty);
    }

    // Meal type filter
    if (selectedMealType !== 'all') {
      filtered = filtered.filter(dish => dish.mealType === selectedMealType);
    }

    setFilteredDishes(filtered);
  };

  const refreshData = async () => {
    vietnameseFoodDataService.clearCache();
    await loadData();
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const getRegionIcon = (region: string) => {
    const icons = {
      north: '🏔️',
      central: '🏛️',
      south: '🌴',
      nationwide: '🇻🇳'
    };
    return icons[region as keyof typeof icons] || '🍽️';
  };

  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      easy: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      hard: 'bg-red-100 text-red-800'
    };
    return colors[difficulty as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getMealTypeIcon = (mealType: string) => {
    const icons = {
      breakfast: '🌅',
      lunch: '☀️',
      dinner: '🌙',
      snack: '🍪',
      anytime: '⏰'
    };
    return icons[mealType as keyof typeof icons] || '🍽️';
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Đang tải dữ liệu món ăn Việt Nam...
          </h3>
          <p className="text-gray-600">
            Vui lòng chờ trong giây lát
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <ChefHat className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Không thể tải dữ liệu
          </h3>
          <p className="text-gray-600 mb-4">
            Có lỗi xảy ra khi tải dữ liệu món ăn Việt Nam
          </p>
          <Button onClick={refreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Thử lại
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {/* Header with Stats */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <ChefHat className="h-6 w-6 text-orange-500" />
              Khám Phá Món Ăn Việt Nam
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {data.stats.totalDishes} món ăn
              </Badge>
              <Button variant="outline" size="sm" onClick={refreshData}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Làm mới
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Coffee className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600">{data.stats.breakfastDishes}</div>
              <div className="text-sm text-gray-600">Món sáng</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <Utensils className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-600">{data.stats.lunchDishes}</div>
              <div className="text-sm text-gray-600">Món trưa</div>
            </div>
            <div className="text-center p-4 bg-indigo-50 rounded-lg">
              <Soup className="h-8 w-8 text-indigo-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-indigo-600">{data.stats.dinnerDishes}</div>
              <div className="text-sm text-gray-600">Món tối</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Star className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-600">{data.stats.snackDishes}</div>
              <div className="text-sm text-gray-600">Món ăn vặt</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <DollarSign className="h-8 w-8 text-orange-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-orange-600">
                {formatPrice(data.stats.priceRange.avg)}
              </div>
              <div className="text-sm text-gray-600">Giá trung bình</div>
            </div>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm món ăn..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedRegion} onValueChange={setSelectedRegion}>
              <SelectTrigger>
                <SelectValue placeholder="Vùng miền" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả vùng miền</SelectItem>
                <SelectItem value="north">🏔️ Miền Bắc</SelectItem>
                <SelectItem value="central">🏛️ Miền Trung</SelectItem>
                <SelectItem value="south">🌴 Miền Nam</SelectItem>
                <SelectItem value="nationwide">🇻🇳 Toàn quốc</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
              <SelectTrigger>
                <SelectValue placeholder="Độ khó" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả độ khó</SelectItem>
                <SelectItem value="easy">Dễ</SelectItem>
                <SelectItem value="medium">Trung bình</SelectItem>
                <SelectItem value="hard">Khó</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedMealType} onValueChange={setSelectedMealType}>
              <SelectTrigger>
                <SelectValue placeholder="Loại bữa ăn" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="breakfast">🌅 Sáng</SelectItem>
                <SelectItem value="lunch">☀️ Trưa</SelectItem>
                <SelectItem value="dinner">🌙 Tối</SelectItem>
                <SelectItem value="snack">🍪 Ăn vặt</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filter Results */}
          <div className="mt-4 flex items-center justify-between">
            <p className="text-sm text-gray-600">
              Hiển thị {filteredDishes.length} / {data.stats.totalDishes} món ăn
            </p>
            {(searchQuery || selectedRegion !== 'all' || selectedDifficulty !== 'all' || selectedMealType !== 'all') && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchQuery('');
                  setSelectedRegion('all');
                  setSelectedDifficulty('all');
                  setSelectedMealType('all');
                }}
              >
                Xóa bộ lọc
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Dishes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDishes.length === 0 ? (
          <div className="col-span-full">
            <Card>
              <CardContent className="p-8 text-center">
                <Search className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Không tìm thấy món ăn nào
                </h3>
                <p className="text-gray-600">
                  Thử điều chỉnh bộ lọc hoặc từ khóa tìm kiếm
                </p>
              </CardContent>
            </Card>
          </div>
        ) : (
          filteredDishes.map((dish) => (
            <Card key={dish.id} className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1">{dish.name}</h3>
                    <p className="text-sm text-gray-600 line-clamp-2">{dish.description}</p>
                  </div>
                  {dish.isPopular && (
                    <Badge variant="secondary" className="ml-2">
                      <Star className="h-3 w-3 mr-1" />
                      Phổ biến
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline" className="text-xs">
                    {getRegionIcon(dish.region)} {dish.region}
                  </Badge>
                  <Badge className={`text-xs ${getDifficultyColor(dish.difficulty)}`}>
                    {dish.difficulty}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {getMealTypeIcon(dish.mealType)} {dish.mealType}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-3">
                  {/* Cooking Info */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600">{dish.cookingTime}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600">{dish.servings} khẩu phần</span>
                    </div>
                  </div>

                  {/* Price */}
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-semibold text-green-600">
                      {formatPrice(dish.cost)}
                    </span>
                    <span className="text-sm text-gray-500">
                      {formatPrice(dish.cost / dish.servings)}/khẩu phần
                    </span>
                  </div>

                  {/* Nutrition Summary */}
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-gray-500">Calories:</span>
                        <span className="font-medium ml-1">{dish.nutrition.calories} kcal</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Protein:</span>
                        <span className="font-medium ml-1">{dish.nutrition.protein}g</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Carbs:</span>
                        <span className="font-medium ml-1">{dish.nutrition.carbs}g</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Fat:</span>
                        <span className="font-medium ml-1">{dish.nutrition.fat}g</span>
                      </div>
                    </div>
                  </div>

                  {/* Tags */}
                  {dish.tags && dish.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {dish.tags.slice(0, 3).map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {dish.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{dish.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Actions */}
                  <div className="btn-grid-2 pt-2">
                    <Button
                      size="sm"
                      className="btn-responsive"
                    >
                      <Eye className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span className="btn-text-truncate">Chi tiết</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="btn-responsive"
                    >
                      <ShoppingCart className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span className="btn-text-truncate">Thêm giỏ</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};