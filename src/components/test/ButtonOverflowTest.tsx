import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Eye, Calendar, Plus, ShoppingCart } from 'lucide-react';

const ButtonOverflowTest: React.FC = () => {
  return (
    <div className="p-4 space-y-6">
      <h2 className="text-2xl font-bold">Button Overflow Test</h2>
      
      {/* Test Case 1: 3 buttons with long text */}
      <Card className="max-w-sm">
        <CardHeader>
          <CardTitle>Test Case 1: 3 Buttons</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 mb-3">
            Nguyên liệu chính: <PERSON><PERSON>, Me, Cà chua, Dứa...
          </p>
          
          {/* Old problematic layout */}
          <div className="mb-4">
            <p className="text-xs text-gray-500 mb-2">Before (Problematic):</p>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex-1">
                <Eye className="h-4 w-4 mr-1" />
                Chi tiết
              </Button>
              <Button size="sm" className="flex-1 bg-green-600">
                <Calendar className="h-4 w-4 mr-1" />
                Thêm vào thực đơn
              </Button>
              <Button size="sm" className="flex-1 bg-orange-600">
                <Plus className="h-4 w-4 mr-1" />
                Hôm nay
              </Button>
            </div>
          </div>
          
          {/* New fixed layout */}
          <div>
            <p className="text-xs text-gray-500 mb-2">After (Fixed):</p>
            <div className="btn-grid-3">
              <Button
                variant="outline"
                size="sm"
                className="btn-responsive text-orange-600 border-orange-300"
              >
                <Eye className="h-3 w-3 mr-1 flex-shrink-0" />
                <span className="btn-text-truncate">Chi tiết</span>
              </Button>
              <Button
                size="sm"
                className="btn-responsive bg-green-600 hover:bg-green-700"
              >
                <Calendar className="h-3 w-3 mr-1 flex-shrink-0" />
                <span className="btn-text-truncate hidden sm:inline">Thêm vào thực đơn</span>
                <span className="btn-text-truncate sm:hidden">Thực đơn</span>
              </Button>
              <Button
                size="sm"
                className="btn-responsive bg-orange-600 hover:bg-orange-700"
              >
                <Plus className="h-3 w-3 mr-1 flex-shrink-0" />
                <span className="btn-text-truncate">Hôm nay</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Test Case 2: 2 buttons */}
      <Card className="max-w-sm">
        <CardHeader>
          <CardTitle>Test Case 2: 2 Buttons</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 mb-3">
            Vietnamese food explorer buttons
          </p>
          
          <div className="btn-grid-2">
            <Button size="sm" className="btn-responsive">
              <Eye className="h-3 w-3 mr-1 flex-shrink-0" />
              <span className="btn-text-truncate">Chi tiết</span>
            </Button>
            <Button variant="outline" size="sm" className="btn-responsive">
              <ShoppingCart className="h-3 w-3 mr-1 flex-shrink-0" />
              <span className="btn-text-truncate">Thêm giỏ</span>
            </Button>
          </div>
        </CardContent>
      </Card>
      
      {/* Test Case 3: Very narrow container */}
      <Card className="max-w-xs">
        <CardHeader>
          <CardTitle className="text-sm">Test Case 3: Very Narrow</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="btn-grid-3">
            <Button size="sm" className="btn-responsive-sm">
              <Eye className="h-3 w-3 mr-1" />
              <span className="btn-text-truncate">Chi tiết</span>
            </Button>
            <Button size="sm" className="btn-responsive-sm bg-green-600">
              <Calendar className="h-3 w-3 mr-1" />
              <span className="btn-text-truncate">Menu</span>
            </Button>
            <Button size="sm" className="btn-responsive-sm bg-orange-600">
              <Plus className="h-3 w-3 mr-1" />
              <span className="btn-text-truncate">Hôm nay</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ButtonOverflowTest;
